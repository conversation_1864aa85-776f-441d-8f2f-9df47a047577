import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';

/// 个人界面页面
class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  // 时间范围选项
  final List<String> _timeRangeOptions = ['最近7天', '最近14天', '最近30天', '最近60天'];
  String _selectedTimeRange = '最近7天';

  // 获取当前选中时间范围的数据
  List<double> get _currentJourneyData {
    switch (_selectedTimeRange) {
      case '最近7天':
        return [2.5, 100.8, 6.2, 5.8, 3.2, 1.5, 29.8];
      case '最近14天':
        return [1.2, 3.5, 2.8, 4.1, 6.3, 2.9, 8.7, 5.4, 3.6, 7.2, 4.8, 6.1, 3.3, 9.2];
      case '最近30天':
        return [
          2.1,
          3.4,
          1.8,
          5.2,
          4.6,
          3.9,
          6.7,
          2.3,
          4.8,
          5.1,
          3.2,
          7.4,
          6.8,
          2.9,
          8.3,
          4.2,
          5.7,
          3.6,
          6.9,
          4.4,
          7.8,
          5.3,
          3.1,
          6.2,
          4.9,
          8.1,
          5.6,
          3.8,
          7.2,
          6.4,
        ];
      case '最近60天':
        return [
          1.8,
          2.9,
          3.4,
          4.1,
          2.7,
          5.3,
          3.8,
          6.2,
          4.5,
          3.1,
          7.4,
          5.9,
          2.6,
          8.1,
          4.3,
          6.7,
          3.5,
          5.8,
          4.2,
          7.3,
          6.1,
          3.9,
          5.4,
          2.8,
          6.8,
          4.7,
          8.2,
          5.1,
          3.6,
          7.5,
          4.9,
          6.3,
          3.2,
          5.7,
          4.4,
          7.9,
          6.6,
          2.4,
          8.3,
          5.2,
          3.7,
          6.9,
          4.8,
          7.1,
          5.5,
          3.3,
          6.4,
          4.1,
          8.7,
          5.8,
          2.9,
          7.6,
          4.6,
          6.2,
          3.4,
          5.9,
          4.3,
          7.8,
          6.1,
          3.8,
        ];
      default:
        return [2.5, 100.8, 6.2, 5.8, 3.2, 1.5, 29.8];
    }
  }

  // 获取当前选中时间范围的标签
  List<String> get _currentJourneyLabels {
    switch (_selectedTimeRange) {
      case '最近7天':
        return ['7/28', '7/29', '7/30', '7/31', '8/1', '8/2', '今天'];
      case '最近14天':
        return _generateDateLabels(14, 2); // 每2天显示一个标签
      case '最近30天':
        return _generateDateLabels(30, 5); // 每5天显示一个标签
      case '最近60天':
        return _generateDateLabels(60, 10); // 每10天显示一个标签
      default:
        return ['7/28', '7/29', '7/30', '7/31', '8/1', '8/2', '今天'];
    }
  }

  /// 生成日期标签，支持间隔显示
  List<String> _generateDateLabels(int days, int interval) {
    final labels = <String>[];
    final now = DateTime.now();

    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: days - 1 - i));
      if (i % interval == 0 || i == days - 1) {
        if (i == days - 1) {
          labels.add('今天');
        } else {
          labels.add('${date.month}/${date.day}');
        }
      } else {
        labels.add(''); // 空标签，不显示但保持位置
      }
    }
    return labels;
  }

  /// 根据数据点数量决定是否显示点标记
  bool _shouldShowDots() {
    return _currentJourneyData.length <= 14; // 14天以内显示点标记
  }

  /// 根据数据点数量调整点标记半径
  double _getDotRadius() {
    if (_currentJourneyData.length <= 7) {
      return 4.0; // 7天：较大的点
    } else if (_currentJourneyData.length <= 14) {
      return 3.0; // 14天：中等大小的点
    } else {
      return 2.0; // 30天以上：较小的点
    }
  }

  /// 根据数据点数量调整线条宽度
  double _getLineWidth() {
    if (_currentJourneyData.length <= 7) {
      return 3.0; // 7天：较粗的线
    } else if (_currentJourneyData.length <= 30) {
      return 2.5; // 14-30天：中等粗细
    } else {
      return 2.0; // 60天：较细的线
    }
  }

  /// 计算纵轴的动态范围和刻度间隔
  Map<String, double> _calculateYAxisConfig(List<double> data) {
    if (data.isEmpty) {
      // 数据为空时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    // 过滤掉无效数据
    final validData = data.where((value) => value.isFinite && !value.isNaN).toList();

    if (validData.isEmpty || validData.every((value) => value == 0)) {
      // 所有数据为零或无效时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    final minValue = validData.reduce((a, b) => a < b ? a : b);
    final maxValue = validData.reduce((a, b) => a > b ? a : b);

    // 计算数据范围
    final dataRange = maxValue - minValue;

    // 为了更好的视觉效果，在最大值上方留出一些空间
    final padding = dataRange * 0.1; // 10%的填充
    final adjustedMaxValue = maxValue + padding;

    // 计算合适的最大Y值（向上取整到合适的数值）
    double maxY;
    if (adjustedMaxValue <= 10) {
      maxY = (adjustedMaxValue / 2).ceil() * 2.0; // 以2为单位向上取整
    } else if (adjustedMaxValue <= 50) {
      maxY = (adjustedMaxValue / 5).ceil() * 5.0; // 以5为单位向上取整
    } else if (adjustedMaxValue <= 100) {
      maxY = (adjustedMaxValue / 10).ceil() * 10.0; // 以10为单位向上取整
    } else {
      maxY = (adjustedMaxValue / 20).ceil() * 20.0; // 以20为单位向上取整
    }

    // 确保最大Y值至少比最大数据值大一点
    if (maxY <= maxValue) {
      maxY = maxValue * 1.2;
    }

    // 计算合适的刻度间隔
    double interval;
    if (maxY <= 10) {
      interval = 1.0;
    } else if (maxY <= 20) {
      interval = 2.0;
    } else if (maxY <= 50) {
      interval = 5.0;
    } else if (maxY <= 100) {
      interval = 10.0;
    } else {
      interval = 20.0;
    }

    // 调整间隔以确保有合理数量的刻度线（4-8个）
    final tickCount = maxY / interval;
    if (tickCount > 8) {
      interval = maxY / 6; // 目标6个刻度
      // 将间隔调整为合适的整数
      if (interval <= 1) {
        interval = 1;
      } else if (interval <= 2) {
        interval = 2;
      } else if (interval <= 5) {
        interval = 5;
      } else if (interval <= 10) {
        interval = 10;
      } else if (interval <= 20) {
        interval = 20;
      } else {
        interval = (interval / 10).ceil() * 10.0;
      }
    } else if (tickCount < 4) {
      interval = maxY / 5; // 目标5个刻度
      // 将间隔调整为合适的小数
      if (interval >= 10) {
        interval = (interval / 10).ceil() * 10.0;
      } else if (interval >= 5) {
        interval = 5;
      } else if (interval >= 2) {
        interval = 2;
      } else if (interval >= 1) {
        interval = 1;
      } else {
        interval = 0.5;
      }
    }

    return {
      'minY': 0.0, // 通常从0开始比较直观
      'maxY': maxY,
      'interval': interval,
    };
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      body: SingleChildScrollView(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息区域
                _buildUserInfoSection(authState.user),
                const SizedBox(height: 32),

                // 旅程统计区域
                _buildJourneyStatsSection(),
                const SizedBox(height: 32),

                // 设置区域
                _buildSettingsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfoSection(UserModel? user) {
    return Row(
      children: [
        // 头像
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[300],
            border: Border.all(color: Colors.black, width: 2),
          ),
          child: ClipOval(
            child: user?.avatar.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: user!.avatar,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                  )
                : Icon(Icons.person, size: 40, color: Colors.grey[600]),
          ),
        ),
        const SizedBox(width: 20),

        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user?.nickname ?? '未知昵称',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'UID: ${user?.uid ?? 'unknown'}',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建旅程统计区域
  Widget _buildJourneyStatsSection() {
    // 计算动态Y轴配置
    final yAxisConfig = _calculateYAxisConfig(_currentJourneyData);
    final minY = yAxisConfig['minY']!;
    final maxY = yAxisConfig['maxY']!;
    final interval = yAxisConfig['interval']!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '旅程统计',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              // 时间范围选择器
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.black, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedTimeRange,
                    isDense: false,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF333333),
                    ),
                    icon: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.black, width: 1),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down_rounded,
                        size: 18,
                        color: Colors.black,
                      ),
                    ),
                    dropdownColor: Colors.white,
                    elevation: 8,
                    borderRadius: BorderRadius.circular(16),
                    items: _timeRangeOptions.map((String option) {
                      final isSelected = option == _selectedTimeRange;
                      return DropdownMenuItem<String>(
                        value: option,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 8,
                          ),
                          margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: isSelected ? Colors.grey[100] : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            border: isSelected
                                ? Border.all(color: Colors.black, width: 1)
                                : null,
                          ),
                          child: Row(
                            children: [
                              if (isSelected)
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  child: const Icon(
                                    Icons.check_circle,
                                    size: 16,
                                    color: Colors.black,
                                  ),
                                ),
                              Text(
                                option,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _selectedTimeRange = newValue;
                        });
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 图表区域
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: interval,
                  verticalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < _currentJourneyLabels.length) {
                          final label = _currentJourneyLabels[index];
                          // 只显示非空标签
                          if (label.isNotEmpty) {
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12,
                                ),
                              ),
                            );
                          }
                        }
                        return Container();
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: interval,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        // 根据数值大小决定显示格式
                        String displayText;
                        if (value == value.toInt()) {
                          displayText = value.toInt().toString();
                        } else {
                          displayText = value.toStringAsFixed(1);
                        }
                        return Text(
                          displayText,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        );
                      },
                      reservedSize: 40, // 增加预留空间以适应更大的数字
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: (_currentJourneyData.length - 1).toDouble(),
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  LineChartBarData(
                    spots: _currentJourneyData.asMap().entries.map((e) {
                      return FlSpot(e.key.toDouble(), e.value);
                    }).toList(),
                    isCurved: true,
                    gradient: LinearGradient(
                      colors: [const Color(0xFF64B5F6), const Color(0xFF42A5F5)],
                    ),
                    barWidth: _getLineWidth(),
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: _shouldShowDots(),
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: _getDotRadius(),
                          color: const Color(0xFF1976D2),
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF64B5F6).withValues(alpha: 0.3),
                          const Color(0xFF42A5F5).withValues(alpha: 0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置区域
  Widget _buildSettingsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '设置',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 20),

          // 设置选项列表 (演示数据)
          _buildSettingItem('个人信息', Icons.person_outline, () {}),
          _buildSettingItem('通知设置', Icons.notifications_outlined, () {}),
          _buildSettingItem('帮助与反馈', Icons.help_outline, () {}),
          _buildSettingItem('关于我们', Icons.info_outline, () {}),
        ],
      ),
    );
  }

  /// 构建设置选项
  Widget _buildSettingItem(String title, IconData icon, Function onTapFunc) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          onTapFunc();
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Row(
            children: [
              Icon(icon, size: 24, color: Colors.grey[600]),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
                ),
              ),
              Icon(Icons.chevron_right, size: 20, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }
}
