import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';

/// 个人界面页面
class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  // 模拟旅程统计数据（最近7天）
  final List<double> _journeyData = [2.5, 100.8, 6.2, 5.8, 3.2, 1.5, 29.8];
  final List<String> _journeyLabels = [
    '7/28',
    '7/29',
    '7/30',
    '7/31',
    '8/1',
    '8/2',
    '今天',
  ];

  /// 计算纵轴的动态范围和刻度间隔
  Map<String, double> _calculateYAxisConfig(List<double> data) {
    if (data.isEmpty) {
      // 数据为空时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    // 过滤掉无效数据
    final validData = data.where((value) => value.isFinite && !value.isNaN).toList();

    if (validData.isEmpty || validData.every((value) => value == 0)) {
      // 所有数据为零或无效时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    final minValue = validData.reduce((a, b) => a < b ? a : b);
    final maxValue = validData.reduce((a, b) => a > b ? a : b);

    // 计算数据范围
    final dataRange = maxValue - minValue;

    // 为了更好的视觉效果，在最大值上方留出一些空间
    final padding = dataRange * 0.1; // 10%的填充
    final adjustedMaxValue = maxValue + padding;

    // 计算合适的最大Y值（向上取整到合适的数值）
    double maxY;
    if (adjustedMaxValue <= 10) {
      maxY = (adjustedMaxValue / 2).ceil() * 2.0; // 以2为单位向上取整
    } else if (adjustedMaxValue <= 50) {
      maxY = (adjustedMaxValue / 5).ceil() * 5.0; // 以5为单位向上取整
    } else if (adjustedMaxValue <= 100) {
      maxY = (adjustedMaxValue / 10).ceil() * 10.0; // 以10为单位向上取整
    } else {
      maxY = (adjustedMaxValue / 20).ceil() * 20.0; // 以20为单位向上取整
    }

    // 确保最大Y值至少比最大数据值大一点
    if (maxY <= maxValue) {
      maxY = maxValue * 1.2;
    }

    // 计算合适的刻度间隔
    double interval;
    if (maxY <= 10) {
      interval = 1.0;
    } else if (maxY <= 20) {
      interval = 2.0;
    } else if (maxY <= 50) {
      interval = 5.0;
    } else if (maxY <= 100) {
      interval = 10.0;
    } else {
      interval = 20.0;
    }

    // 调整间隔以确保有合理数量的刻度线（4-8个）
    final tickCount = maxY / interval;
    if (tickCount > 8) {
      interval = maxY / 6; // 目标6个刻度
      // 将间隔调整为合适的整数
      if (interval <= 1) {
        interval = 1;
      } else if (interval <= 2) {
        interval = 2;
      } else if (interval <= 5) {
        interval = 5;
      } else if (interval <= 10) {
        interval = 10;
      } else if (interval <= 20) {
        interval = 20;
      } else {
        interval = (interval / 10).ceil() * 10.0;
      }
    } else if (tickCount < 4) {
      interval = maxY / 5; // 目标5个刻度
      // 将间隔调整为合适的小数
      if (interval >= 10) {
        interval = (interval / 10).ceil() * 10.0;
      } else if (interval >= 5) {
        interval = 5;
      } else if (interval >= 2) {
        interval = 2;
      } else if (interval >= 1) {
        interval = 1;
      } else {
        interval = 0.5;
      }
    }

    return {
      'minY': 0.0, // 通常从0开始比较直观
      'maxY': maxY,
      'interval': interval,
    };
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      body: SingleChildScrollView(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息区域
                _buildUserInfoSection(authState.user),
                const SizedBox(height: 32),

                // 旅程统计区域
                _buildJourneyStatsSection(),
                const SizedBox(height: 32),

                // 设置区域
                _buildSettingsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfoSection(UserModel? user) {
    return Row(
      children: [
        // 头像
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[300],
            border: Border.all(color: Colors.black, width: 2),
          ),
          child: ClipOval(
            child: user?.avatar.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: user!.avatar,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                  )
                : Icon(Icons.person, size: 40, color: Colors.grey[600]),
          ),
        ),
        const SizedBox(width: 20),

        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user?.nickname ?? '未知昵称',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'UID: ${user?.uid ?? 'unknown'}',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建旅程统计区域
  Widget _buildJourneyStatsSection() {
    // 计算动态Y轴配置
    final yAxisConfig = _calculateYAxisConfig(_journeyData);
    final minY = yAxisConfig['minY']!;
    final maxY = yAxisConfig['maxY']!;
    final interval = yAxisConfig['interval']!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '旅程统计',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              Text('最近七天', style: TextStyle(fontSize: 14, color: Colors.grey[600])),
            ],
          ),
          const SizedBox(height: 24),

          // 图表区域
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  horizontalInterval: interval,
                  verticalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                  },
                  getDrawingVerticalLine: (value) {
                    return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < _journeyLabels.length) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              _journeyLabels[index],
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w400,
                                fontSize: 12,
                              ),
                            ),
                          );
                        }
                        return Container();
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: interval,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        // 根据数值大小决定显示格式
                        String displayText;
                        if (value == value.toInt()) {
                          displayText = value.toInt().toString();
                        } else {
                          displayText = value.toStringAsFixed(1);
                        }
                        return Text(
                          displayText,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        );
                      },
                      reservedSize: 40, // 增加预留空间以适应更大的数字
                    ),
                  ),
                ),
                borderData: FlBorderData(show: false),
                minX: 0,
                maxX: (_journeyData.length - 1).toDouble(),
                minY: minY,
                maxY: maxY,
                lineBarsData: [
                  LineChartBarData(
                    spots: _journeyData.asMap().entries.map((e) {
                      return FlSpot(e.key.toDouble(), e.value);
                    }).toList(),
                    isCurved: true,
                    gradient: LinearGradient(
                      colors: [const Color(0xFF64B5F6), const Color(0xFF42A5F5)],
                    ),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: const Color(0xFF1976D2),
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF64B5F6).withValues(alpha: 0.3),
                          const Color(0xFF42A5F5).withValues(alpha: 0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置区域
  Widget _buildSettingsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '设置',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 20),

          // 设置选项列表 (演示数据)
          _buildSettingItem('个人信息', Icons.person_outline, () {}),
          _buildSettingItem('通知设置', Icons.notifications_outlined, () {}),
          _buildSettingItem('帮助与反馈', Icons.help_outline, () {}),
          _buildSettingItem('关于我们', Icons.info_outline, () {}),
        ],
      ),
    );
  }

  /// 构建设置选项
  Widget _buildSettingItem(String title, IconData icon, Function onTapFunc) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          onTapFunc();
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          child: Row(
            children: [
              Icon(icon, size: 24, color: Colors.grey[600]),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
                ),
              ),
              Icon(Icons.chevron_right, size: 20, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }
}
